"""
Query Service

Handles all query processing business logic including:
- AI query processing and response generation
- Model management and configuration
- Anti-hallucination mode handling
- Session management for queries
- Analytics data collection
- Response formatting and validation

This service extracts query processing business logic from route handlers
to improve testability and maintainability.
"""

import logging
import ollama
from query import query_category
from session_utils import get_or_create_session
import db_utils
import geo_utils
from .exceptions import ServiceError, QueryProcessingError
from services.cache_service import get_cache_service, cached

# Configure logging
logger = logging.getLogger(__name__)

class QueryService:
    """Service class for query processing business logic."""
    
    def __init__(self):
        """Initialize the query service."""
        self.initialized = False
        self.default_models = {
            'llm': 'llama3.1:8b-instruct-q4_K_M',
            'embedding': 'mxbai-embed-large:latest',
            'vision': 'llama3.2-vision:11b-instruct-q4_K_M'
        }
    
    def initialize(self):
        """Initialize the service with required dependencies."""
        try:
            # Load default models configuration
            self._load_model_configuration()
            
            self.initialized = True
            logger.info("Query service initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Error initializing query service: {str(e)}")
            return False
    
    def process_query(self, category, question, query_params=None):
        """
        Process a user query and return AI response with metadata.
        
        Args:
            category: Document category to query
            question: User's question
            query_params: Dictionary with query parameters
            
        Returns:
            Dictionary with response data and metadata
        """
        try:
            if not category or not question:
                raise QueryProcessingError("Category and question are required")
            
            # Extract query parameters
            query_params = query_params or {}
            client_name = query_params.get('client_name', 'Anonymous')
            anti_hallucination_mode = query_params.get('anti_hallucination_mode', 'strict')
            current_model = query_params.get('current_model', self.default_models['llm'])
            current_embedding = query_params.get('current_embedding', self.default_models['embedding'])
            current_vision = query_params.get('current_vision', self.default_models['vision'])
            
            # Get or create session
            session_id, session_start, device_fingerprint = get_or_create_session()
            
            logger.info(f"Processing query for category: {category}, client: {client_name}")
            
            # Process the query using the existing query_category function
            result = query_category(
                category=category,
                question=question,
                anti_hallucination_mode=anti_hallucination_mode,
                current_model=current_model,
                current_embedding=current_embedding,
                current_vision=current_vision
            )
            
            # Extract response components
            response_data = {
                'answer': result.get('answer', ''),
                'sources': result.get('sources', []),
                'images': result.get('images', []),
                'pdf_links': result.get('pdf_links', []),
                'metadata': result.get('metadata', {}),
                'url_images': result.get('url_images', []),
                'pdf_images': result.get('pdf_images', []),
                'document_thumbnails': result.get('document_thumbnails', []),
                'session_id': session_id,
                'session_start': session_start
            }
            
            # Save chat history
            chat_id = self._save_chat_history(
                category, question, response_data, client_name,
                session_id, session_start, device_fingerprint,
                anti_hallucination_mode, current_model, current_embedding, current_vision
            )
            
            # Save analytics data
            if chat_id:
                self._save_analytics_data(
                    chat_id, category, question, response_data,
                    device_fingerprint, client_name, current_model,
                    current_embedding, current_vision
                )
            
            logger.info(f"Query processed successfully for category: {category}")
            return response_data
            
        except Exception as e:
            logger.error(f"Error processing query: {str(e)}")
            raise QueryProcessingError(f"Query processing failed: {str(e)}")
    
    @cached(ttl=300)  # Cache for 5 minutes
    def get_available_models(self):
        """
        Get list of available AI models from Ollama.

        Returns:
            Dictionary with available models and metadata
        """
        try:
            # Get models from Ollama
            models_response = ollama.list()
            models = models_response.get('models', [])

            # Filter and format models
            available_models = []
            for model in models:
                model_name = model.get('name', '')
                if model_name:
                    available_models.append({
                        'name': model_name,
                        'size': model.get('size', 0),
                        'modified_at': model.get('modified_at', ''),
                        'digest': model.get('digest', '')
                    })

            logger.debug(f"Retrieved {len(available_models)} available models")
            return {
                'success': True,
                'models': available_models,
                'count': len(available_models)
            }
            
        except Exception as e:
            logger.error(f"Error fetching available models: {str(e)}")
            return {
                'success': False,
                'error': f'Error fetching models: {str(e)}',
                'models': []
            }
    
    def validate_query_parameters(self, query_params):
        """
        Validate query parameters.
        
        Args:
            query_params: Dictionary with query parameters
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            if not isinstance(query_params, dict):
                return False, "Query parameters must be a dictionary"
            
            # Check required parameters
            if 'question' not in query_params or not query_params['question'].strip():
                return False, "Question is required and cannot be empty"
            
            # Validate anti-hallucination mode
            valid_modes = ['strict', 'balanced', 'off']
            ah_mode = query_params.get('anti_hallucination_mode', 'strict')
            if ah_mode not in valid_modes:
                return False, f"Invalid anti-hallucination mode. Must be one of: {valid_modes}"
            
            # Validate model names (basic check)
            model_fields = ['current_model', 'current_embedding', 'current_vision']
            for field in model_fields:
                if field in query_params:
                    model_name = query_params[field]
                    if not isinstance(model_name, str) or not model_name.strip():
                        return False, f"Invalid {field}: must be a non-empty string"
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error validating query parameters: {str(e)}")
            return False, f"Validation error: {str(e)}"
    
    def get_query_statistics(self, category=None, date_range=None):
        """
        Get query statistics for analytics.
        
        Args:
            category: Optional category filter
            date_range: Optional date range filter
            
        Returns:
            Dictionary with query statistics
        """
        try:
            # Get query statistics from database
            stats = db_utils.get_query_statistics(category=category, date_range=date_range)
            
            logger.debug(f"Retrieved query statistics for category: {category}")
            return stats
            
        except Exception as e:
            logger.error(f"Error getting query statistics: {str(e)}")
            return {}
    
    @cached(ttl=3600)  # Cache for 1 hour
    def _load_model_configuration(self):
        """Load model configuration from file."""
        try:
            import json
            import os

            config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'default_models.json')

            if os.path.exists(config_file):
                with open(config_file, 'r') as f:
                    config = json.load(f)

                # Update default models if specified in config
                if 'llm_model' in config:
                    self.default_models['llm'] = config['llm_model']
                if 'embedding_model' in config:
                    self.default_models['embedding'] = config['embedding_model']
                if 'vision_model' in config:
                    self.default_models['vision'] = config['vision_model']

                logger.info("Model configuration loaded successfully")
            else:
                logger.info("No model configuration file found, using defaults")
                
        except Exception as e:
            logger.warning(f"Error loading model configuration: {str(e)}")
    
    def _save_chat_history(self, category, question, response_data, client_name,
                          session_id, session_start, device_fingerprint,
                          anti_hallucination_mode, current_model, current_embedding, current_vision):
        """Save chat history to database."""
        try:
            chat_id = db_utils.save_chat_history(
                category, question, response_data['answer'], response_data['sources'],
                response_data['images'], response_data['pdf_links'], response_data['metadata'],
                response_data['url_images'], response_data['pdf_images'], client_name,
                session_id, session_start, device_fingerprint, response_data['document_thumbnails'],
                anti_hallucination_mode, current_model, current_embedding, current_vision
            )
            
            logger.debug(f"Saved chat history with ID: {chat_id}")
            return chat_id
            
        except Exception as e:
            logger.error(f"Error saving chat history: {str(e)}")
            return None
    
    def _save_analytics_data(self, chat_id, category, question, response_data,
                           device_fingerprint, client_name, current_model,
                           current_embedding, current_vision):
        """Save analytics data to database."""
        try:
            # Get geolocation data
            ip_address, city, region, country, latitude, longitude = geo_utils.get_location_for_analytics()
            
            # Save analytics data
            db_utils.save_analytics_data(
                chat_id, category, len(question), len(response_data['answer']),
                response_data['metadata'].get('processing_time', 0),
                ip_address, city, region, country, latitude, longitude,
                device_fingerprint, client_name, current_model,
                current_embedding, current_vision
            )
            
            logger.debug(f"Saved analytics data for chat ID: {chat_id}")
            
        except Exception as e:
            logger.error(f"Error saving analytics data: {str(e)}")
