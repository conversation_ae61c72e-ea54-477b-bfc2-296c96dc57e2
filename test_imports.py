#!/usr/bin/env python3
"""
Test script to verify that the circular import issue is resolved.
"""

import sys
import traceback

def test_import(module_name, description):
    """Test importing a module and report results."""
    try:
        print(f"Testing {description}...")
        __import__(module_name)
        print(f"✅ {description} imported successfully")
        return True
    except Exception as e:
        print(f"❌ {description} failed: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        return False

def main():
    """Run all import tests."""
    print("🧪 Testing Circular Import Fix")
    print("=" * 50)
    
    tests = [
        ("services.exceptions", "Services exceptions module"),
        ("services.auth_service", "Auth service module"),
        ("services.file_service", "File service module"),
        ("services.query_service", "Query service module"),
        ("services.analytics_service", "Analytics service module"),
        ("services", "Services package"),
    ]
    
    success_count = 0
    total_count = len(tests)
    
    for module_name, description in tests:
        if test_import(module_name, description):
            success_count += 1
        print()
    
    print(f"📊 Results: {success_count}/{total_count} imports successful")
    
    if success_count == total_count:
        print("🎉 All imports successful - circular import issue resolved!")
        return True
    else:
        print("⚠️  Some imports failed - circular import issue may persist")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
